package com.tqhit.battery.one.features.stats.charge.presentation

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.SeekBar
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.FragmentStatsChargeBinding
import com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.service.VibrationService
import com.tqhit.battery.one.utils.PermissionUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel
import com.tqhit.battery.one.features.navigation.AppNavigator
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Fragment for displaying charge statistics and estimates.
 * Shows current battery status, active charge session, and time estimates.
 * 
 * ARCHITECTURE COMPLIANCE:
 * - Extends AdLibBaseFragment for consistent ViewBinding pattern
 * - Follows stats module architecture with proper lifecycle management
 * - Implements comprehensive null safety and defensive programming
 * - Uses structured logging for debugging and ADB testing
 * 
 * CRASH FIX IMPLEMENTATION:
 * - Replaced manual findViewById() with ViewBinding for null safety
 * - Added comprehensive binding validation and error handling
 * - Implemented proper fragment lifecycle state validation
 * - Added defensive programming practices with fallback behavior
 */
@AndroidEntryPoint
class StatsChargeFragment : AdLibBaseFragment<FragmentStatsChargeBinding>() {

    companion object {
        private const val TAG = "StatsChargeFragment"

        fun newInstance(): StatsChargeFragment {
            return StatsChargeFragment()
        }
    }

    // ViewBinding implementation following established pattern
    override val binding by lazy { 
        Log.d(TAG, "BINDING_INIT: Initializing FragmentStatsChargeBinding")
        try {
            FragmentStatsChargeBinding.inflate(layoutInflater)
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_INIT: Critical error initializing ViewBinding", e)
            throw e
        }
    }

    private val viewModel: StatsChargeViewModel by viewModels()
    private val appViewModel: AppViewModel by viewModels()
    private val sharedNavigationViewModel: SharedNavigationViewModel by activityViewModels()

    @Inject
    lateinit var vibrationService: VibrationService

    @Inject
    lateinit var appNavigator: AppNavigator

    private val permissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                showBatteryAlarmDialog()
            } else {
                NotificationDialog(
                    requireActivity(),
                    getString(R.string.notification),
                    getString(R.string.notify_access)
                ).show()
            }
        }

    /**
     * DEFENSIVE PROGRAMMING: Safe binding access with comprehensive validation
     * This method ensures binding is valid before any UI operations
     */
    private fun safeBindingAccess(operation: String, action: (FragmentStatsChargeBinding) -> Unit) {
        try {
            if (!isAdded) {
                Log.w(TAG, "BINDING_SAFETY: Fragment not added, skipping $operation")
                return
            }
            
            if (view == null) {
                Log.w(TAG, "BINDING_SAFETY: Fragment view is null, skipping $operation")
                return
            }
            
            if (!::binding.isInitialized) {
                Log.e(TAG, "BINDING_SAFETY: ViewBinding not initialized for $operation")
                return
            }
            
            action(binding)
            Log.v(TAG, "BINDING_SAFETY: Successfully executed $operation")
            
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_SAFETY: Error during $operation", e)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: StatsChargeFragment.onViewCreated() started at $startTime")

        super.onViewCreated(view, savedInstanceState)

        // CRASH FIX: Use safe binding access for all UI operations
        safeBindingAccess("setupUI") { binding ->
            val setupStartTime = System.currentTimeMillis()
            setupSeekBar()
            setupResetButton()
            setupBatteryAlarmButton()
            setupBackNavigation()
            Log.d(TAG, "STARTUP_TIMING: UI setup took ${System.currentTimeMillis() - setupStartTime}ms")
        }

        val observeStartTime = System.currentTimeMillis()
        observeUiState()
        observeNavigationState()
        Log.d(TAG, "STARTUP_TIMING: observeUiState() and observeNavigationState() took ${System.currentTimeMillis() - observeStartTime}ms")
        Log.d(TAG, "SharedNavigationViewModel: Navigation state observation established")

        Log.d(TAG, "StatsChargeFragment view created")
        Log.d(TAG, "STARTUP_TIMING: StatsChargeFragment.onViewCreated() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * Sets up the target percentage SeekBar with null safety.
     */
    private fun setupSeekBar() {
        safeBindingAccess("setupSeekBar") { binding ->
            binding.seekbarTarget.min = 1
            binding.seekbarTarget.max = 100
            
            binding.seekbarTarget.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                    if (fromUser) {
                        safeBindingAccess("updateTargetPercentageText") { binding ->
                            binding.tvTargetPercentage.text = "${progress}%"
                        }
                    }
                }
                
                override fun onStartTrackingTouch(seekBar: SeekBar?) {
                    // No action needed
                }
                
                override fun onStopTrackingTouch(seekBar: SeekBar?) {
                    seekBar?.let { bar ->
                        viewModel.setTargetChargePercentage(bar.progress)
                        Log.d(TAG, "Target percentage set to: ${bar.progress}%")
                    }
                }
            })
        }
    }

    /**
     * Sets up the reset session button with null safety.
     */
    private fun setupResetButton() {
        safeBindingAccess("setupResetButton") { binding ->
            binding.btnResetSession.setOnClickListener {
                viewModel.resetChargeSession()
                Log.d(TAG, "Reset session button clicked")
            }
        }
    }

    /**
     * Sets up the battery alarm button with null safety.
     */
    private fun setupBatteryAlarmButton() {
        safeBindingAccess("setupBatteryAlarmButton") { binding ->
            binding.batteryAlarmBtn.setOnClickListener {
                showBatteryAlarmDialog()
            }
        }
    }

    /**
     * Shows the battery alarm dialog with permission handling.
     */
    private fun showBatteryAlarmDialog() {
        if (PermissionUtils.isNotificationPermissionGranted(requireContext())) {
            SelectBatteryAlarmDialog(requireActivity(), permissionLauncher, appViewModel, vibrationService).show()
        } else {
            PermissionUtils.requestNotificationPermission(
                context = requireContext(),
                permissionLauncher = permissionLauncher,
                onPermissionGranted = { SelectBatteryAlarmDialog(requireActivity(), permissionLauncher, appViewModel, vibrationService).show() },
                onPermissionDenied = { NotificationDialog(requireActivity(), getString(R.string.notification), getString(R.string.notify_access)).show() }
            )
        }
    }

    /**
     * Observes UI state changes and updates the views with null safety.
     */
    private fun observeUiState() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { uiState ->
                    updateUI(uiState)
                }
            }
        }
    }

    /**
     * Observes navigation state changes from SharedNavigationViewModel.
     * This replaces the external FragmentLifecycleOptimizer with self-managed fragment state.
     */
    private fun observeNavigationState() {
        Log.d(TAG, "SharedNavigationViewModel: Setting up navigation state observation")

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                sharedNavigationViewModel.activeFragmentId.collect { activeFragmentId ->
                    val isThisFragmentActive = activeFragmentId == R.id.chargeFragment

                    Log.d(TAG, "SharedNavigationViewModel: Navigation state changed - activeFragment: ${getFragmentName(activeFragmentId)}")
                    Log.d(TAG, "SharedNavigationViewModel: StatsChargeFragment is ${if (isThisFragmentActive) "ACTIVE" else "INACTIVE"}")

                    if (isThisFragmentActive) {
                        onFragmentVisible()
                    } else {
                        onFragmentHidden()
                    }
                }
            }
        }
    }

    /**
     * Called when this fragment becomes visible/active.
     * Triggers UI refresh to prevent staleness.
     */
    private fun onFragmentVisible() {
        Log.d(TAG, "SharedNavigationViewModel: StatsChargeFragment is now VISIBLE")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment activated via SharedNavigationViewModel")

        // Trigger UI refresh to prevent staleness
        val currentState = viewModel.uiState.value
        updateUI(currentState)
    }

    /**
     * Called when this fragment becomes hidden/inactive.
     */
    private fun onFragmentHidden() {
        Log.d(TAG, "SharedNavigationViewModel: StatsChargeFragment is now HIDDEN")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment deactivated via SharedNavigationViewModel")
    }

    /**
     * Gets a human-readable fragment name for logging.
     */
    private fun getFragmentName(fragmentId: Int): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }
}
